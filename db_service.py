import os
import json
from pymongo import MongoClient
from flask import current_app, g

# MongoDB连接
def get_mongo_db():
    """获取MongoDB连接"""
    if 'mongo_db' not in g:
        client = MongoClient(
            host=current_app.config['MONGODB_HOST'],
            port=current_app.config['MONGODB_PORT']
        )
        g.mongo_db = client[current_app.config['MONGODB_DB']]
    return g.mongo_db

# 本地存储适配器
class LocalStorageAdapter:
    """本地存储适配器，模拟MongoDB API但使用本地文件存储"""
    
    def __init__(self, app):
        self.data_dir = os.path.join(app.instance_path, 'local_storage')
        os.makedirs(self.data_dir, exist_ok=True)
        self.collections = {}
        
    def _get_collection_path(self, collection_name):
        """获取集合文件路径"""
        return os.path.join(self.data_dir, f"{collection_name}.json")
    
    def _load_collection(self, collection_name):
        """加载集合数据"""
        path = self._get_collection_path(collection_name)
        if os.path.exists(path):
            with open(path, 'r') as f:
                return json.load(f)
        return []
    
    def _save_collection(self, collection_name, data):
        """保存集合数据"""
        path = self._get_collection_path(collection_name)
        with open(path, 'w') as f:
            json.dump(data, f, indent=2)
    
    def get_collection(self, collection_name):
        """获取集合对象"""
        if collection_name not in self.collections:
            self.collections[collection_name] = LocalCollection(self, collection_name)
        return self.collections[collection_name]
    
    def __getitem__(self, collection_name):
        """支持字典访问语法"""
        return self.get_collection(collection_name)

class LocalCollection:
    """本地集合类，模拟MongoDB集合API"""
    
    def __init__(self, adapter, name):
        self.adapter = adapter
        self.name = name
        
    def find(self, query=None, projection=None):
        """查询文档"""
        data = self.adapter._load_collection(self.name)
        if query is None:
            return data
        
        # 简单查询实现
        result = []
        for item in data:
            match = True
            for key, value in query.items():
                if key not in item or item[key] != value:
                    match = False
                    break
            if match:
                result.append(item)
        return result
    
    def find_one(self, query=None):
        """查询单个文档"""
        results = self.find(query)
        return results[0] if results else None
    
    def insert_one(self, document):
        """插入单个文档"""
        data = self.adapter._load_collection(self.name)
        
        # 生成ID
        if '_id' not in document:
            document['_id'] = str(len(data) + 1)
            
        data.append(document)
        self.adapter._save_collection(self.name, data)
        return {'inserted_id': document['_id']}
    
    def update_one(self, query, update):
        """更新单个文档"""
        data = self.adapter._load_collection(self.name)
        modified_count = 0
        
        for i, item in enumerate(data):
            match = True
            for key, value in query.items():
                if key not in item or item[key] != value:
                    match = False
                    break
                    
            if match:
                # 处理$set操作符
                if '$set' in update:
                    for key, value in update['$set'].items():
                        data[i][key] = value
                modified_count = 1
                break
                
        self.adapter._save_collection(self.name, data)
        return {'modified_count': modified_count}
    
    def delete_one(self, query):
        """删除单个文档"""
        data = self.adapter._load_collection(self.name)
        deleted_count = 0
        
        for i, item in enumerate(data):
            match = True
            for key, value in query.items():
                if key not in item or item[key] != value:
                    match = False
                    break
                    
            if match:
                data.pop(i)
                deleted_count = 1
                break
                
        self.adapter._save_collection(self.name, data)
        return {'deleted_count': deleted_count}

# 存储工厂
def get_db():
    """获取当前存储实例（MongoDB或本地存储）"""
    storage_mode = current_app.config['STORAGE_MODE']
    
    if storage_mode == 'mongodb':
        return get_mongo_db()
    else:  # local
        if 'local_db' not in g:
            g.local_db = LocalStorageAdapter(current_app)
        return g.local_db

def init_db(app):
    """初始化数据库"""
    # 确保实例路径存在
    os.makedirs(app.instance_path, exist_ok=True)
    
    # 注册关闭时的资源释放
    @app.teardown_appcontext
    def close_db(e=None):
        if 'mongo_db' in g:
            # 关闭MongoDB连接
            client = g.pop('mongo_db').client
            client.close()
