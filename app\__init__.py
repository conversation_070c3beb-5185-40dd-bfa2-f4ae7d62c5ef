from flask import Flask
from flask_cors import CORS
from flask_login import <PERSON>ginManager
from .config.config import config_by_name

# 初始化登录管理器
login_manager = LoginManager()

@login_manager.user_loader
def load_user(user_id):
    """加载用户回调函数"""
    from .models.user import User
    return User.get_by_id(user_id)

def create_app(config_name='development'):
    app = Flask(__name__)
    app.config.from_object(config_by_name[config_name])
    
    # 启用CORS
    CORS(app)
    
    # 初始化登录管理器
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = '请先登录'
    
    # 初始化数据库服务
    from .services.db_service import init_db
    init_db(app)
    
    # 注册API蓝图
    from .api import items_bp, categories_bp, tags_bp, plugins_bp, auth_bp
    app.register_blueprint(items_bp, url_prefix='/api')
    app.register_blueprint(categories_bp, url_prefix='/api')
    app.register_blueprint(tags_bp, url_prefix='/api')
    app.register_blueprint(plugins_bp, url_prefix='/api')
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    
    # 注册健康检查路由
    @app.route('/health')
    def health_check():
        return {'status': 'ok', 'message': 'Service is running'}
    
    # 注册前端路由
    from flask import send_from_directory
    import os
    
    @app.route('/')
    def index():
        return send_from_directory(app.static_folder, 'index.html')
    
    @app.route('/<path:path>')
    def serve_static(path):
        if os.path.exists(os.path.join(app.static_folder, path)):
            return send_from_directory(app.static_folder, path)
        elif path.endswith('.html'):
            return send_from_directory(app.static_folder, path)
        else:
            return send_from_directory(app.static_folder, 'index.html')
    
    return app
