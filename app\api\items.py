from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
from ..services.db_service import get_db
from datetime import datetime
from bson import ObjectId

items_bp = Blueprint('items', __name__)

@items_bp.route('/items', methods=['GET'])
@login_required
def get_items():
    """获取项目列表"""
    try:
        db = get_db()
        items = list(db['items'].find({'created_by': current_user.id}))

        # 转换ObjectId为字符串
        for item in items:
            if '_id' in item:
                item['_id'] = str(item['_id'])

        return jsonify({'items': items}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@items_bp.route('/items/<item_id>', methods=['GET'])
@login_required
def get_item(item_id):
    """获取单个项目"""
    try:
        db = get_db()
        item = db['items'].find_one({'_id': item_id, 'created_by': current_user.id})

        if not item:
            return jsonify({'error': '项目不存在'}), 404

        if '_id' in item:
            item['_id'] = str(item['_id'])

        return jsonify({'item': item}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@items_bp.route('/items', methods=['POST'])
@login_required
def create_item():
    """创建项目"""
    try:
        db = get_db()
        data = request.get_json()

        item = {
            'title': data.get('title'),
            'url': data.get('url', ''),
            'urgency': data.get('urgency', 1),
            'importance': data.get('importance', 1),
            'reminder': data.get('reminder'),
            'category_id': data.get('category_id'),
            'tags': data.get('tags', []),
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow(),
            'created_by': current_user.id
        }

        result = db['items'].insert_one(item)
        item['_id'] = str(result.inserted_id)

        return jsonify({'message': '项目创建成功', 'item': item}), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@items_bp.route('/items/<item_id>', methods=['PUT'])
@login_required
def update_item(item_id):
    """更新项目"""
    try:
        db = get_db()
        data = request.get_json()

        # 检查项目是否存在且属于当前用户
        existing_item = db['items'].find_one({'_id': item_id, 'created_by': current_user.id})
        if not existing_item:
            return jsonify({'error': '项目不存在'}), 404

        update_data = {
            'title': data.get('title'),
            'url': data.get('url', ''),
            'urgency': data.get('urgency', 1),
            'importance': data.get('importance', 1),
            'reminder': data.get('reminder'),
            'category_id': data.get('category_id'),
            'tags': data.get('tags', []),
            'updated_at': datetime.utcnow()
        }

        db['items'].update_one(
            {'_id': item_id},
            {'$set': update_data}
        )

        return jsonify({'message': '项目更新成功'}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@items_bp.route('/items/<item_id>', methods=['DELETE'])
@login_required
def delete_item(item_id):
    """删除项目"""
    try:
        db = get_db()

        # 检查项目是否存在且属于当前用户
        existing_item = db['items'].find_one({'_id': item_id, 'created_by': current_user.id})
        if not existing_item:
            return jsonify({'error': '项目不存在'}), 404

        db['items'].delete_one({'_id': item_id})

        return jsonify({'message': '项目删除成功'}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500
