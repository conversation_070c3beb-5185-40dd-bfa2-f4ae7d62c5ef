/* 响应式设计样式 */

/* 小屏幕设备（手机，小于768px） */
@media (max-width: 767.98px) {
  .container {
    padding-left: 15px;
    padding-right: 15px;
  }
  
  .card-header {
    padding: 0.75rem;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  /* 调整导航栏在小屏幕上的显示 */
  .navbar-brand {
    font-size: 1.25rem;
  }
  
  /* 调整表单元素在小屏幕上的间距 */
  .form-group {
    margin-bottom: 1rem;
  }
  
  /* 调整按钮在小屏幕上的大小 */
  .btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }
  
  /* 调整模态框在小屏幕上的宽度 */
  .modal-dialog {
    margin: 0.5rem;
  }
  
  /* 调整分页在小屏幕上的显示 */
  .pagination {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  /* 调整项目卡片在小屏幕上的显示 */
  .item-card {
    margin-bottom: 0.75rem;
  }
  
  /* 调整标签在小屏幕上的显示 */
  .tag-badge {
    margin-bottom: 0.5rem;
  }
}

/* 中等屏幕设备（平板，768px到991px） */
@media (min-width: 768px) and (max-width: 991.98px) {
  /* 调整卡片在中等屏幕上的间距 */
  .card {
    margin-bottom: 1.25rem;
  }
  
  /* 调整导航栏在中等屏幕上的显示 */
  .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
}

/* 大屏幕设备（桌面，992px及以上） */
@media (min-width: 992px) {
  /* 增强大屏幕上的视觉效果 */
  .card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  }
  
  /* 调整分类树在大屏幕上的显示 */
  .category-tree {
    max-height: 600px;
    overflow-y: auto;
  }
}

/* 触摸设备优化 */
@media (hover: none) {
  /* 为触摸设备优化点击区域 */
  .btn, .nav-link, .category-item, .tag-badge {
    padding: 0.5rem 0.75rem;
  }
  
  /* 移除悬停效果，因为触摸设备没有悬停状态 */
  .card:hover, .item-card:hover {
    transform: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  }
}

/* 打印样式优化 */
@media print {
  body {
    background-color: white;
  }
  
  .card {
    box-shadow: none;
    border: 1px solid #dee2e6;
  }
  
  .no-print {
    display: none !important;
  }
  
  .container {
    width: 100%;
    max-width: 100%;
  }
}
