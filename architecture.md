# 整合项目架构设计

## 1. 系统概述

本项目将第一个Flask+MongoDB项目（数据管理系统）与第二个插件化前端框架项目进行整合，形成一个统一的系统。整合后的系统具有以下特点：

- 基于Flask的后端，支持MongoDB数据存储
- 模块化的插件系统，支持多种前端技术栈（cshtml、Vue、Next.js）
- 用户认证与权限管理
- 支持免费版（本地存储）和EE版（MongoDB存储）
- 优先提供Windows安装包部署方式

## 2. 系统架构

### 2.1 整体架构

```
+----------------------------------+
|           客户端浏览器            |
+----------------------------------+
               |
               v
+----------------------------------+
|           统一前端界面            |
|  +----------------------------+  |
|  |        核心功能模块        |  |
|  +----------------------------+  |
|  |        插件管理模块        |  |
|  +----------------------------+  |
|  |      可切换前端插件        |  |
|  |  (cshtml / Vue / Next.js)  |  |
|  +----------------------------+  |
+----------------------------------+
               |
               v
+----------------------------------+
|           Flask 后端             |
|  +----------------------------+  |
|  |       用户认证模块         |  |
|  +----------------------------+  |
|  |       数据管理API          |  |
|  +----------------------------+  |
|  |       插件管理API          |  |
|  +----------------------------+  |
|  |       权限控制模块         |  |
|  +----------------------------+  |
+----------------------------------+
               |
               v
+----------------------------------+
|           数据存储层             |
|  +----------------------------+  |
|  |    本地存储 (免费版)       |  |
|  +----------------------------+  |
|  |   MongoDB存储 (EE版)       |  |
|  +----------------------------+  |
+----------------------------------+
```

### 2.2 模块划分

#### 2.2.1 后端模块

1. **核心模块**
   - 应用初始化与配置
   - 路由管理
   - 中间件（CORS、日志、异常处理）

2. **用户认证模块**
   - 用户注册/登录/登出
   - 会话管理
   - 密码重置

3. **数据管理模块**
   - 项目CRUD操作
   - 分类管理
   - 标签管理
   - 搜索与排序

4. **插件管理模块**
   - 插件上传/安装/卸载
   - 插件启用/禁用
   - 插件配置管理

5. **权限控制模块**
   - 基于角色的访问控制
   - 插件权限管理
   - API访问控制

6. **存储适配器**
   - 本地存储实现（IndexedDB/LocalStorage）
   - MongoDB存储实现
   - 存储策略切换

#### 2.2.2 前端模块

1. **核心UI框架**
   - 布局组件
   - 导航组件
   - 认证界面
   - 通用组件库

2. **数据管理界面**
   - 项目列表/详情
   - 分类树状浏览
   - 搜索与筛选
   - 标签管理

3. **插件管理界面**
   - 插件列表
   - 插件上传
   - 插件配置
   - 皮肤管理

4. **插件系统**
   - 插件加载器
   - 插件生命周期管理
   - 插件通信机制
   - 插件API适配器

### 2.3 数据模型

#### 2.3.1 用户模型
```json
{
  "id": "用户ID",
  "username": "用户名",
  "password_hash": "密码哈希",
  "email": "电子邮件",
  "role": "角色（admin/user）",
  "created_at": "创建时间",
  "last_login": "最后登录时间",
  "preferences": {
    "active_plugin": "当前激活的插件",
    "theme": "当前主题"
  }
}
```

#### 2.3.2 项目模型
```json
{
  "id": "项目ID",
  "title": "标题",
  "url": "URL",
  "urgency": "紧急度",
  "importance": "重要度",
  "reminder": "提醒日期",
  "category_id": "分类ID",
  "tags": ["标签1", "标签2"],
  "created_at": "创建时间",
  "updated_at": "更新时间",
  "created_by": "创建者ID"
}
```

#### 2.3.3 分类模型
```json
{
  "id": "分类ID",
  "name": "分类名称",
  "parent_id": "父分类ID",
  "level": "层级（0-根分类，1-大类，2-小类）",
  "created_at": "创建时间",
  "created_by": "创建者ID"
}
```

#### 2.3.4 标签模型
```json
{
  "id": "标签ID",
  "name": "标签名称",
  "color": "标签颜色",
  "created_at": "创建时间",
  "created_by": "创建者ID"
}
```

#### 2.3.5 插件模型
```json
{
  "id": "插件ID",
  "name": "插件名称",
  "version": "版本号",
  "description": "描述",
  "tech": "技术栈（cshtml/vue/nextjs）",
  "entry_point": "入口文件路径",
  "enabled": "是否启用",
  "config": "插件配置",
  "permissions": ["权限1", "权限2"],
  "created_at": "安装时间",
  "updated_at": "更新时间"
}
```

### 2.4 API设计

#### 2.4.1 认证API
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/user` - 获取当前用户信息
- `PUT /api/auth/user` - 更新用户信息
- `POST /api/auth/reset-password` - 重置密码

#### 2.4.2 数据管理API
- `GET /api/items` - 获取项目列表
- `GET /api/items/:id` - 获取单个项目
- `POST /api/items` - 创建项目
- `PUT /api/items/:id` - 更新项目
- `DELETE /api/items/:id` - 删除项目
- `GET /api/categories` - 获取分类树
- `POST /api/categories` - 创建分类
- `PUT /api/categories/:id` - 更新分类
- `DELETE /api/categories/:id` - 删除分类
- `GET /api/tags` - 获取标签列表
- `POST /api/tags` - 创建标签
- `PUT /api/tags/:id` - 更新标签
- `DELETE /api/tags/:id` - 删除标签

#### 2.4.3 插件管理API
- `GET /api/plugins` - 获取插件列表
- `GET /api/plugins/:id` - 获取单个插件
- `POST /api/plugins` - 上传安装插件
- `PUT /api/plugins/:id` - 更新插件配置
- `DELETE /api/plugins/:id` - 卸载插件
- `PUT /api/plugins/:id/enable` - 启用插件
- `PUT /api/plugins/:id/disable` - 禁用插件
- `GET /api/skins` - 获取皮肤列表
- `PUT /api/skins/:id/apply` - 应用皮肤

### 2.5 权限设计

#### 2.5.1 用户角色
- **管理员(admin)**: 拥有所有权限
- **普通用户(user)**: 拥有基本数据操作权限和使用插件权限

#### 2.5.2 权限列表
- `manage_users` - 管理用户
- `manage_plugins` - 管理插件（上传、安装、卸载）
- `use_plugins` - 使用插件
- `manage_items` - 管理所有项目
- `manage_own_items` - 管理自己的项目
- `manage_categories` - 管理分类
- `manage_tags` - 管理标签

#### 2.5.3 角色-权限映射
- **管理员**: 所有权限
- **普通用户**: `use_plugins`, `manage_own_items`

### 2.6 插件系统设计

#### 2.6.1 插件结构
每个插件包含以下文件：
- `manifest.json` - 插件元数据
- `index.js` - 插件入口文件
- 其他资源文件（CSS、图片等）

#### 2.6.2 插件生命周期
- `install` - 安装插件
- `load` - 加载插件
- `initialize` - 初始化插件
- `render` - 渲染插件UI
- `update` - 更新插件数据
- `unload` - 卸载插件
- `uninstall` - 删除插件

#### 2.6.3 插件通信机制
- 插件通过统一的API适配器与后端通信
- 插件之间通过事件总线进行通信
- 插件与主应用通过预定义的接口进行交互

### 2.7 存储策略

#### 2.7.1 本地存储（免费版）
- 使用浏览器的IndexedDB存储数据
- 支持数据导出为JSON或HTML
- 数据仅保存在用户浏览器中

#### 2.7.2 MongoDB存储（EE版）
- 使用MongoDB存储所有数据
- 支持多用户访问和数据共享
- 支持数据备份和恢复

### 2.8 部署方案

#### 2.8.1 Windows安装包（优先）
- 包含Python环境、MongoDB、依赖库
- 提供批处理脚本进行安装和启动
- 支持本地部署和使用

#### 2.8.2 未来扩展
- Docker容器部署
- Web服务部署

## 3. 技术选型

### 3.1 后端技术
- **语言**: Python 3.11
- **Web框架**: Flask 2.3.3
- **数据库**: MongoDB 4.5.0
- **缓存**: Redis 4.6.0
- **认证**: Flask-Login
- **API**: RESTful API
- **文档**: Swagger/OpenAPI

### 3.2 前端技术
- **核心框架**: 原生JavaScript + HTML + CSS
- **插件技术**:
  - cshtml (Razor Pages)
  - Vue.js
  - Next.js
- **UI组件**: 自定义组件库
- **状态管理**: 基于事件的状态管理
- **构建工具**: Webpack

### 3.3 开发工具
- **版本控制**: Git
- **包管理**: pip, npm
- **测试**: pytest, Jest
- **CI/CD**: 批处理脚本

## 4. 实现路线图

### 4.1 阶段一：后端整合
- 重构Flask后端，整合MongoDB连接
- 实现用户认证和权限控制
- 统一API接口设计

### 4.2 阶段二：前端整合
- 整合数据管理界面和插件管理界面
- 实现插件加载器和生命周期管理
- 开发插件API适配器

### 4.3 阶段三：插件开发
- 实现cshtml插件
- 实现Vue插件
- 实现Next.js插件

### 4.4 阶段四：部署与文档
- 创建Windows安装包
- 编写用户文档和开发者文档
- 准备部署脚本

## 5. 风险与挑战

### 5.1 技术风险
- 多种前端技术栈的兼容性问题
- 本地存储和MongoDB存储的切换逻辑
- 插件安全性和隔离性

### 5.2 解决方案
- 使用统一的API适配器抽象不同技术栈的差异
- 采用工厂模式和策略模式实现存储切换
- 实现插件沙箱和权限控制机制
