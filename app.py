#!/usr/bin/env python3
"""
Flask MongoDB 整合应用主入口
"""

import os
from app import create_app

# 获取配置环境
config_name = os.environ.get('FLASK_ENV') or 'development'

# 创建应用实例
app = create_app(config_name)

if __name__ == '__main__':
    # 开发环境配置
    host = os.environ.get('HOST', '0.0.0.0')
    port = int(os.environ.get('PORT', 5000))
    debug = config_name == 'development'
    
    print(f"启动Flask应用...")
    print(f"配置环境: {config_name}")
    print(f"监听地址: http://{host}:{port}")
    print(f"MongoDB连接: {app.config['MONGODB_HOST']}:{app.config['MONGODB_PORT']}")
    print(f"数据库: {app.config['MONGODB_DB']}")
    print(f"存储模式: {app.config['STORAGE_MODE']}")
    
    app.run(host=host, port=port, debug=debug)
