<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>信息管理系统 - 预览版</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" />
    <link rel="stylesheet" href="css/site.css" />
    <link rel="stylesheet" href="css/responsive.css" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/">信息管理系统 - 预览版</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                        aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link active" href="/">首页</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/categories.html">分类浏览</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/search.html">搜索</a>
                        </li>
                    </ul>
                    <div class="d-flex">
                        <span class="navbar-text me-3" id="version-badge">
                            <span class="badge bg-success">免费版</span>
                        </span>
                        <button class="btn btn-outline-light" id="switchVersionBtn">
                            切换到EE版
                        </button>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <div class="container mt-4">
        <main role="main" class="pb-3">
            <div class="row">
                <div class="col-md-8">
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">信息列表</h5>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-outline-primary" id="addNewItemBtn">
                                    <i class="bi bi-plus-circle"></i> 添加新项目
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                    <i class="bi bi-sort-down"></i> 排序
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item sort-option" data-sort="importance" data-order="desc" href="#">按重要度 (高到低)</a></li>
                                    <li><a class="dropdown-item sort-option" data-sort="importance" data-order="asc" href="#">按重要度 (低到高)</a></li>
                                    <li><a class="dropdown-item sort-option" data-sort="urgency" data-order="desc" href="#">按紧急度 (高到低)</a></li>
                                    <li><a class="dropdown-item sort-option" data-sort="urgency" data-order="asc" href="#">按紧急度 (低到高)</a></li>
                                    <li><a class="dropdown-item sort-option" data-sort="reminder" data-order="asc" href="#">按提醒日期 (近到远)</a></li>
                                    <li><a class="dropdown-item sort-option" data-sort="title" data-order="asc" href="#">按标题 (A-Z)</a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="list-group" id="itemsList">
                                <!-- 示例项目 -->
                                <div class="list-group-item item-card importance-5">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <h5 class="mb-1">
                                            <span class="importance-indicator importance-5" title="重要度: 5"></span>
                                            <span class="urgency-indicator urgency-4" title="紧急度: 4"></span>
                                            完成项目报告
                                        </h5>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary edit-item-btn">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger delete-item-btn">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <p class="mb-1">
                                        <a href="https://example.com/report" target="_blank" class="text-decoration-none">
                                            <i class="bi bi-link-45deg"></i> https://example.com/report
                                        </a>
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="bi bi-calendar-event"></i> 2025-05-21
                                            <span class="ms-2"><i class="bi bi-folder"></i> 根 > 工作 > 项目</span>
                                        </small>
                                    </div>
                                    <div class="mt-2">
                                        <span class="tag-badge">报告</span>
                                        <span class="tag-badge">截止日期</span>
                                        <span class="tag-badge">重要</span>
                                    </div>
                                </div>
                                
                                <div class="list-group-item item-card importance-4">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <h5 class="mb-1">
                                            <span class="importance-indicator importance-4" title="重要度: 4"></span>
                                            <span class="urgency-indicator urgency-2" title="紧急度: 2"></span>
                                            学习Python高级特性
                                        </h5>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary edit-item-btn">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger delete-item-btn">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <p class="mb-1">
                                        <a href="https://docs.python.org/3/tutorial/" target="_blank" class="text-decoration-none">
                                            <i class="bi bi-link-45deg"></i> https://docs.python.org/3/tutorial/
                                        </a>
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="bi bi-calendar-event"></i> 2025-05-25
                                            <span class="ms-2"><i class="bi bi-folder"></i> 根 > 学习 > 课程</span>
                                        </small>
                                    </div>
                                    <div class="mt-2">
                                        <span class="tag-badge">Python</span>
                                        <span class="tag-badge">编程</span>
                                        <span class="tag-badge">学习</span>
                                    </div>
                                </div>
                                
                                <div class="list-group-item item-card importance-3">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <h5 class="mb-1">
                                            <span class="importance-indicator importance-3" title="重要度: 3"></span>
                                            <span class="urgency-indicator urgency-3" title="紧急度: 3"></span>
                                            购买生日礼物
                                        </h5>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary edit-item-btn">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger delete-item-btn">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <p class="mb-1">
                                        <a href="https://www.amazon.com" target="_blank" class="text-decoration-none">
                                            <i class="bi bi-link-45deg"></i> https://www.amazon.com
                                        </a>
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="bi bi-calendar-event"></i> 2025-05-30
                                            <span class="ms-2"><i class="bi bi-folder"></i> 根 > 生活 > 购物</span>
                                        </small>
                                    </div>
                                    <div class="mt-2">
                                        <span class="tag-badge">礼物</span>
                                        <span class="tag-badge">生日</span>
                                        <span class="tag-badge">购物</span>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex justify-content-center mt-3">
                                <nav aria-label="Page navigation">
                                    <ul class="pagination">
                                        <li class="page-item disabled">
                                            <a class="page-link" href="#" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                                        <li class="page-item">
                                            <a class="page-link" href="#" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">快速搜索</h5>
                        </div>
                        <div class="card-body">
                            <div class="input-group mb-3">
                                <input type="text" class="form-control" id="quickSearch" placeholder="输入标题关键词...">
                                <button class="btn btn-primary" type="button" id="quickSearchBtn">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">热门标签</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex flex-wrap gap-2" id="tagsList">
                                <span class="tag-badge">报告</span>
                                <span class="tag-badge">Python</span>
                                <span class="tag-badge">学习</span>
                                <span class="tag-badge">编程</span>
                                <span class="tag-badge">重要</span>
                                <span class="tag-badge">截止日期</span>
                                <span class="tag-badge">礼物</span>
                                <span class="tag-badge">生日</span>
                                <span class="tag-badge">购物</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">即将到期提醒</h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group" id="remindersList">
                                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <div class="fw-bold">完成项目报告</div>
                                        <small class="text-muted">2025-05-21</small>
                                    </div>
                                    <span class="badge bg-danger rounded-pill">今天</span>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <div class="fw-bold">学习Python高级特性</div>
                                        <small class="text-muted">2025-05-25</small>
                                    </div>
                                    <span class="badge bg-warning text-dark rounded-pill">4天后</span>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <div class="fw-bold">购买生日礼物</div>
                                        <small class="text-muted">2025-05-30</small>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">9天后</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 添加/编辑项目模态框 -->
            <div class="modal fade" id="itemModal" tabindex="-1" aria-labelledby="itemModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="itemModalLabel">添加新项目</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form id="itemForm">
                                <input type="hidden" id="itemId">
                                <div class="mb-3">
                                    <label for="itemTitle" class="form-label">标题</label>
                                    <input type="text" class="form-control" id="itemTitle" required>
                                </div>
                                <div class="mb-3">
                                    <label for="itemUrl" class="form-label">URL</label>
                                    <input type="url" class="form-control" id="itemUrl" required>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="itemUrgency" class="form-label">紧急度 (1-5)</label>
                                        <input type="range" class="form-range" min="1" max="5" id="itemUrgency" value="3">
                                        <div class="d-flex justify-content-between">
                                            <span>低</span>
                                            <span id="urgencyValue">3</span>
                                            <span>高</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="itemImportance" class="form-label">重要度 (1-5)</label>
                                        <input type="range" class="form-range" min="1" max="5" id="itemImportance" value="3">
                                        <div class="d-flex justify-content-between">
                                            <span>低</span>
                                            <span id="importanceValue">3</span>
                                            <span>高</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="itemReminder" class="form-label">提醒日期</label>
                                    <input type="date" class="form-control" id="itemReminder">
                                </div>
                                <div class="mb-3">
                                    <label for="itemCategory" class="form-label">分类</label>
                                    <select class="form-select" id="itemCategory">
                                        <option value="">选择分类...</option>
                                        <option>根 > 工作 > 项目</option>
                                        <option>根 > 工作 > 会议</option>
                                        <option>根 > 工作 > 任务</option>
                                        <option>根 > 学习 > 课程</option>
                                        <option>根 > 学习 > 书籍</option>
                                        <option>根 > 学习 > 笔记</option>
                                        <option>根 > 生活 > 健康</option>
                                        <option>根 > 生活 > 娱乐</option>
                                        <option>根 > 生活 > 购物</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="itemTags" class="form-label">标签 (用逗号分隔)</label>
                                    <input type="text" class="form-control" id="itemTags" placeholder="例如: 工作,学习,重要">
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="saveItemBtn">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <footer class="border-top footer text-muted">
        <div class="container">
            &copy; 2025 - 信息管理系统 - 预览版
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    
    <script>
        // 简单的交互演示脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 模态框初始化
            const itemModal = new bootstrap.Modal(document.getElementById('itemModal'));
            
            // 添加新项目按钮
            document.getElementById('addNewItemBtn').addEventListener('click', function() {
                document.getElementById('itemModalLabel').textContent = '添加新项目';
                document.getElementById('itemForm').reset();
                itemModal.show();
            });
            
            // 编辑按钮
            document.querySelectorAll('.edit-item-btn').forEach(button => {
                button.addEventListener('click', function() {
                    document.getElementById('itemModalLabel').textContent = '编辑项目';
                    // 这里只是演示，实际应该加载项目数据
                    itemModal.show();
                });
            });
            
            // 删除按钮
            document.querySelectorAll('.delete-item-btn').forEach(button => {
                button.addEventListener('click', function() {
                    if (confirm('确定要删除这个项目吗？此操作不可撤销。')) {
                        alert('删除操作在预览版中不可用');
                    }
                });
            });
            
            // 保存按钮
            document.getElementById('saveItemBtn').addEventListener('click', function() {
                alert('保存操作在预览版中不可用');
                itemModal.hide();
            });
            
            // 版本切换按钮
            document.getElementById('switchVersionBtn').addEventListener('click', function() {
                const badge = document.getElementById('version-badge').querySelector('.badge');
                if (badge.classList.contains('bg-success')) {
                    badge.classList.remove('bg-success');
                    badge.classList.add('bg-danger');
                    badge.textContent = 'EE版';
                    this.textContent = '切换到免费版';
                } else {
                    badge.classList.remove('bg-danger');
                    badge.classList.add('bg-success');
                    badge.textContent = '免费版';
                    this.textContent = '切换到EE版';
                }
                alert('版本切换在预览版中仅改变显示，不改变功能');
            });
            
            // 快速搜索
            document.getElementById('quickSearchBtn').addEventListener('click', function() {
                const keyword = document.getElementById('quickSearch').value.trim();
                if (keyword) {
                    alert(`搜索"${keyword}"在预览版中不可用`);
                }
            });
            
            // 标签点击
            document.querySelectorAll('.tag-badge').forEach(tag => {
                tag.addEventListener('click', function() {
                    alert(`标签筛选"${this.textContent}"在预览版中不可用`);
                });
            });
            
            // 重要度和紧急度滑块
            const urgencySlider = document.getElementById('itemUrgency');
            const urgencyValue = document.getElementById('urgencyValue');
            if (urgencySlider && urgencyValue) {
                urgencySlider.addEventListener('input', function() {
                    urgencyValue.textContent = this.value;
                });
            }
            
            const importanceSlider = document.getElementById('itemImportance');
            const importanceValue = document.getElementById('importanceValue');
            if (importanceSlider && importanceValue) {
                importanceSlider.addEventListener('input', function() {
                    importanceValue.textContent = this.value;
                });
            }
        });
    </script>
</body>
</html>
