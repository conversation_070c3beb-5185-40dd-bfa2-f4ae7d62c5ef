#!/usr/bin/env python3
"""
测试MongoDB连接和应用基本功能
"""

import sys
from app import create_app
from app.services.db_service import get_db
from app.models.user import User

def test_mongodb_connection():
    """测试MongoDB连接"""
    print("测试MongoDB连接...")
    
    try:
        app = create_app('development')
        with app.app_context():
            db = get_db()
            
            # 测试连接
            collections = db.list_collection_names()
            print(f"✓ MongoDB连接成功")
            print(f"✓ 数据库: {app.config['MONGODB_DB']}")
            print(f"✓ 现有集合: {collections}")
            
            return True
    except Exception as e:
        print(f"✗ MongoDB连接失败: {e}")
        return False

def test_user_model():
    """测试用户模型"""
    print("\n测试用户模型...")
    
    try:
        app = create_app('development')
        with app.app_context():
            # 创建测试用户
            test_user = User(
                username='test_user',
                email='<EMAIL>'
            )
            test_user.set_password('test_password')
            
            # 保存用户
            test_user.save()
            print(f"✓ 用户创建成功: {test_user.username}")
            
            # 查询用户
            found_user = User.get_by_username('test_user')
            if found_user:
                print(f"✓ 用户查询成功: {found_user.username}")
                
                # 验证密码
                if found_user.check_password('test_password'):
                    print("✓ 密码验证成功")
                else:
                    print("✗ 密码验证失败")
                    
                return True
            else:
                print("✗ 用户查询失败")
                return False
                
    except Exception as e:
        print(f"✗ 用户模型测试失败: {e}")
        return False

def test_api_endpoints():
    """测试API端点"""
    print("\n测试API端点...")
    
    try:
        app = create_app('development')
        client = app.test_client()
        
        # 测试健康检查
        response = client.get('/health')
        if response.status_code == 200:
            print("✓ 健康检查端点正常")
        else:
            print(f"✗ 健康检查端点异常: {response.status_code}")
            return False
        
        # 测试主页
        response = client.get('/')
        if response.status_code == 200:
            print("✓ 主页端点正常")
        else:
            print(f"✗ 主页端点异常: {response.status_code}")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ API端点测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试Flask MongoDB应用...")
    print("=" * 50)
    
    tests = [
        test_mongodb_connection,
        test_user_model,
        test_api_endpoints
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！应用可以正常运行。")
        return 0
    else:
        print("✗ 部分测试失败，请检查配置。")
        return 1

if __name__ == '__main__':
    sys.exit(main())
