from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
from bson import ObjectId
from ..services.db_service import get_db

class User(UserMixin):
    """用户模型"""
    
    def __init__(self, username, email, password=None, role='user', _id=None, 
                 created_at=None, last_login=None, preferences=None):
        self.id = str(_id) if _id else None
        self.username = username
        self.email = email
        self.password_hash = password
        self.role = role
        self.created_at = created_at or datetime.utcnow()
        self.last_login = last_login
        self.preferences = preferences or {
            'active_plugin': None,
            'theme': 'default'
        }
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def has_permission(self, permission):
        """检查用户是否有指定权限"""
        # 管理员拥有所有权限
        if self.role == 'admin':
            return True
        
        # 普通用户权限
        if self.role == 'user':
            if permission in ['use_plugins', 'manage_own_items']:
                return True
        
        return False
    
    def to_dict(self):
        """转换为字典"""
        return {
            '_id': self.id,
            'username': self.username,
            'email': self.email,
            'password_hash': self.password_hash,
            'role': self.role,
            'created_at': self.created_at,
            'last_login': self.last_login,
            'preferences': self.preferences
        }
    
    @classmethod
    def from_dict(cls, data):
        """从字典创建用户对象"""
        return cls(
            username=data.get('username'),
            email=data.get('email'),
            password=data.get('password_hash'),
            role=data.get('role', 'user'),
            _id=data.get('_id'),
            created_at=data.get('created_at'),
            last_login=data.get('last_login'),
            preferences=data.get('preferences')
        )
    
    @classmethod
    def get_by_id(cls, user_id):
        """根据ID获取用户"""
        db = get_db()
        user_data = db['users'].find_one({'_id': user_id})
        return cls.from_dict(user_data) if user_data else None
    
    @classmethod
    def get_by_username(cls, username):
        """根据用户名获取用户"""
        db = get_db()
        user_data = db['users'].find_one({'username': username})
        return cls.from_dict(user_data) if user_data else None
    
    @classmethod
    def get_by_email(cls, email):
        """根据邮箱获取用户"""
        db = get_db()
        user_data = db['users'].find_one({'email': email})
        return cls.from_dict(user_data) if user_data else None
    
    def save(self):
        """保存用户到数据库"""
        db = get_db()
        user_dict = self.to_dict()
        
        if self.id:
            # 更新现有用户
            db['users'].update_one(
                {'_id': self.id},
                {'$set': user_dict}
            )
        else:
            # 创建新用户
            result = db['users'].insert_one(user_dict)
            self.id = str(result.inserted_id)
        
        return self
    
    def update_last_login(self):
        """更新最后登录时间"""
        self.last_login = datetime.utcnow()
        db = get_db()
        db['users'].update_one(
            {'_id': self.id},
            {'$set': {'last_login': self.last_login}}
        )
    
    def update_preferences(self, preferences):
        """更新用户偏好设置"""
        self.preferences.update(preferences)
        db = get_db()
        db['users'].update_one(
            {'_id': self.id},
            {'$set': {'preferences': self.preferences}}
        )
