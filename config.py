import os
from datetime import timedelta

# 基础配置类
class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'hard_to_guess_string'
    DEBUG = False
    TESTING = False
    
    # MongoDB配置
    MONGODB_HOST = os.environ.get('MONGODB_HOST') or 'localhost'
    MONGODB_PORT = int(os.environ.get('MONGODB_PORT') or 27017)
    MONGODB_DB = os.environ.get('MONGODB_DB') or 'integrated_db'
    
    # Redis配置
    REDIS_HOST = os.environ.get('REDIS_HOST') or 'localhost'
    REDIS_PORT = int(os.environ.get('REDIS_PORT') or 6379)
    REDIS_DB = int(os.environ.get('REDIS_DB') or 0)
    
    # 插件配置
    PLUGIN_FOLDER = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'app', 'plugins')
    SKIN_FOLDER = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'app', 'static', 'skins')
    CONFIG_FILE = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'app', 'config', 'plugin_config.json')
    
    # 用户会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(days=7)
    
    # 存储模式配置
    STORAGE_MODE = os.environ.get('STORAGE_MODE') or 'mongodb'  # 'mongodb' 或 'local'
    
    @staticmethod
    def init_app(app):
        pass

# 开发环境配置
class DevelopmentConfig(Config):
    DEBUG = True
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # 开发环境特定配置
        app.config['TEMPLATES_AUTO_RELOAD'] = True

# 测试环境配置
class TestingConfig(Config):
    TESTING = True
    MONGODB_DB = 'test_db'
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)

# 生产环境配置
class ProductionConfig(Config):
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # 生产环境特定配置
        import logging
        from logging.handlers import RotatingFileHandler
        
        # 设置日志
        file_handler = RotatingFileHandler('logs/app.log', maxBytes=10*1024*1024, backupCount=10)
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        app.logger.setLevel(logging.INFO)
        app.logger.info('Integrated Application startup')

# 配置字典
config_by_name = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
