from flask import Flask, render_template, send_from_directory, redirect
import os

app = Flask(__name__, static_folder='static')

# 前端页面路由
@app.route('/')
def index():
    return send_from_directory('static', 'index.html')

@app.route('/<path:path>')
def serve_static(path):
    if os.path.exists(os.path.join('static', path)):
        return send_from_directory('static', path)
    elif path.endswith('.html'):
        return send_from_directory('static', path)
    else:
        return send_from_directory('static', 'index.html')

# API健康检查
@app.route('/api/health')
def health_check():
    return {"status": "success", "message": "API服务正常运行"}

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001, debug=True)
