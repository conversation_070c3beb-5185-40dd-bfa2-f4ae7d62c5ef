<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>信息管理系统 - 搜索</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" />
    <link rel="stylesheet" href="css/site.css" />
    <link rel="stylesheet" href="css/responsive.css" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/">信息管理系统 - 预览版</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                        aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="/">首页</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/categories.html">分类浏览</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/search.html">搜索</a>
                        </li>
                    </ul>
                    <div class="d-flex">
                        <span class="navbar-text me-3" id="version-badge">
                            <span class="badge bg-success">免费版</span>
                        </span>
                        <button class="btn btn-outline-light" id="switchVersionBtn">
                            切换到EE版
                        </button>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <div class="container mt-4">
        <main role="main" class="pb-3">
            <div class="row">
                <div class="col-md-4">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">高级搜索</h5>
                        </div>
                        <div class="card-body">
                            <form id="searchForm">
                                <div class="mb-3">
                                    <label for="searchKeyword" class="form-label">关键词</label>
                                    <input type="text" class="form-control" id="searchKeyword" placeholder="输入标题关键词...">
                                </div>
                                <div class="mb-3">
                                    <label for="searchCategory" class="form-label">分类</label>
                                    <select class="form-select" id="searchCategory">
                                        <option value="">所有分类</option>
                                        <option>根 > 工作</option>
                                        <option>根 > 工作 > 会议</option>
                                        <option>根 > 工作 > 项目</option>
                                        <option>根 > 工作 > 任务</option>
                                        <option>根 > 学习</option>
                                        <option>根 > 学习 > 课程</option>
                                        <option>根 > 学习 > 书籍</option>
                                        <option>根 > 学习 > 笔记</option>
                                        <option>根 > 生活</option>
                                        <option>根 > 生活 > 健康</option>
                                        <option>根 > 生活 > 娱乐</option>
                                        <option>根 > 生活 > 购物</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="searchTags" class="form-label">标签</label>
                                    <input type="text" class="form-control" id="searchTags" placeholder="输入标签，用逗号分隔...">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">重要度</label>
                                    <div class="d-flex">
                                        <select class="form-select me-2" id="importanceMin">
                                            <option value="">最小</option>
                                            <option value="1">1</option>
                                            <option value="2">2</option>
                                            <option value="3">3</option>
                                            <option value="4">4</option>
                                            <option value="5">5</option>
                                        </select>
                                        <span class="align-self-center">至</span>
                                        <select class="form-select ms-2" id="importanceMax">
                                            <option value="">最大</option>
                                            <option value="1">1</option>
                                            <option value="2">2</option>
                                            <option value="3">3</option>
                                            <option value="4">4</option>
                                            <option value="5">5</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">紧急度</label>
                                    <div class="d-flex">
                                        <select class="form-select me-2" id="urgencyMin">
                                            <option value="">最小</option>
                                            <option value="1">1</option>
                                            <option value="2">2</option>
                                            <option value="3">3</option>
                                            <option value="4">4</option>
                                            <option value="5">5</option>
                                        </select>
                                        <span class="align-self-center">至</span>
                                        <select class="form-select ms-2" id="urgencyMax">
                                            <option value="">最大</option>
                                            <option value="1">1</option>
                                            <option value="2">2</option>
                                            <option value="3">3</option>
                                            <option value="4">4</option>
                                            <option value="5">5</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">提醒日期</label>
                                    <div class="d-flex">
                                        <input type="date" class="form-control me-2" id="reminderStart">
                                        <span class="align-self-center">至</span>
                                        <input type="date" class="form-control ms-2" id="reminderEnd">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="searchSort" class="form-label">排序方式</label>
                                    <select class="form-select" id="searchSort">
                                        <option value="importance_desc">按重要度 (高到低)</option>
                                        <option value="importance_asc">按重要度 (低到高)</option>
                                        <option value="urgency_desc">按紧急度 (高到低)</option>
                                        <option value="urgency_asc">按紧急度 (低到高)</option>
                                        <option value="reminder_asc">按提醒日期 (近到远)</option>
                                        <option value="title_asc">按标题 (A-Z)</option>
                                    </select>
                                </div>
                                <button type="button" class="btn btn-primary w-100" id="searchBtn">
                                    <i class="bi bi-search"></i> 搜索
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0" id="searchResultTitle">搜索结果</h5>
                            <span class="badge bg-primary" id="resultCount">3 个结果</span>
                        </div>
                        <div class="card-body">
                            <div class="list-group" id="searchResultsList">
                                <!-- 示例搜索结果 -->
                                <div class="list-group-item item-card importance-5">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <h5 class="mb-1">
                                            <span class="importance-indicator importance-5" title="重要度: 5"></span>
                                            <span class="urgency-indicator urgency-4" title="紧急度: 4"></span>
                                            完成项目报告
                                        </h5>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary edit-item-btn">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger delete-item-btn">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <p class="mb-1">
                                        <a href="https://example.com/report" target="_blank" class="text-decoration-none">
                                            <i class="bi bi-link-45deg"></i> https://example.com/report
                                        </a>
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="bi bi-calendar-event"></i> 2025-05-21
                                            <span class="ms-2"><i class="bi bi-folder"></i> 根 > 工作 > 项目</span>
                                        </small>
                                    </div>
                                    <div class="mt-2">
                                        <span class="tag-badge">报告</span>
                                        <span class="tag-badge">截止日期</span>
                                        <span class="tag-badge">重要</span>
                                    </div>
                                </div>
                                
                                <div class="list-group-item item-card importance-4">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <h5 class="mb-1">
                                            <span class="importance-indicator importance-4" title="重要度: 4"></span>
                                            <span class="urgency-indicator urgency-2" title="紧急度: 2"></span>
                                            学习Python高级特性
                                        </h5>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary edit-item-btn">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger delete-item-btn">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <p class="mb-1">
                                        <a href="https://docs.python.org/3/tutorial/" target="_blank" class="text-decoration-none">
                                            <i class="bi bi-link-45deg"></i> https://docs.python.org/3/tutorial/
                                        </a>
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="bi bi-calendar-event"></i> 2025-05-25
                                            <span class="ms-2"><i class="bi bi-folder"></i> 根 > 学习 > 课程</span>
                                        </small>
                                    </div>
                                    <div class="mt-2">
                                        <span class="tag-badge">Python</span>
                                        <span class="tag-badge">编程</span>
                                        <span class="tag-badge">学习</span>
                                    </div>
                                </div>
                                
                                <div class="list-group-item item-card importance-3">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <h5 class="mb-1">
                                            <span class="importance-indicator importance-3" title="重要度: 3"></span>
                                            <span class="urgency-indicator urgency-3" title="紧急度: 3"></span>
                                            购买生日礼物
                                        </h5>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary edit-item-btn">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger delete-item-btn">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <p class="mb-1">
                                        <a href="https://www.amazon.com" target="_blank" class="text-decoration-none">
                                            <i class="bi bi-link-45deg"></i> https://www.amazon.com
                                        </a>
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="bi bi-calendar-event"></i> 2025-05-30
                                            <span class="ms-2"><i class="bi bi-folder"></i> 根 > 生活 > 购物</span>
                                        </small>
                                    </div>
                                    <div class="mt-2">
                                        <span class="tag-badge">礼物</span>
                                        <span class="tag-badge">生日</span>
                                        <span class="tag-badge">购物</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <footer class="border-top footer text-muted">
        <div class="container">
            &copy; 2025 - 信息管理系统 - 预览版
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    
    <script>
        // 简单的交互演示脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 搜索按钮
            document.getElementById('searchBtn').addEventListener('click', function() {
                const keyword = document.getElementById('searchKeyword').value.trim();
                const category = document.getElementById('searchCategory').value;
                const tags = document.getElementById('searchTags').value.trim();
                
                // 更新搜索结果标题
                let searchTitle = '搜索结果';
                if (keyword) {
                    searchTitle += ` - "${keyword}"`;
                }
                if (category) {
                    searchTitle += ` - 分类: ${category}`;
                }
                if (tags) {
                    searchTitle += ` - 标签: ${tags}`;
                }
                
                document.getElementById('searchResultTitle').textContent = searchTitle;
                
                // 在实际应用中，这里应该发送搜索请求并更新结果
                // 这里只是演示，显示一个提示
                alert('搜索功能在预览版中不可用，显示的是示例结果');
            });
            
            // 版本切换按钮
            document.getElementById('switchVersionBtn').addEventListener('click', function() {
                const badge = document.getElementById('version-badge').querySelector('.badge');
                if (badge.classList.contains('bg-success')) {
                    badge.classList.remove('bg-success');
                    badge.classList.add('bg-danger');
                    badge.textContent = 'EE版';
                    this.textContent = '切换到免费版';
                } else {
                    badge.classList.remove('bg-danger');
                    badge.classList.add('bg-success');
                    badge.textContent = '免费版';
                    this.textContent = '切换到EE版';
                }
                alert('版本切换在预览版中仅改变显示，不改变功能');
            });
            
            // 编辑按钮
            document.querySelectorAll('.edit-item-btn').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    alert('编辑操作在预览版中不可用');
                });
            });
            
            // 删除按钮
            document.querySelectorAll('.delete-item-btn').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    if (confirm('确定要删除这个项目吗？此操作不可撤销。')) {
                        alert('删除操作在预览版中不可用');
                    }
                });
            });
            
            // 标签点击
            document.querySelectorAll('.tag-badge').forEach(tag => {
                tag.addEventListener('click', function() {
                    document.getElementById('searchTags').value = this.textContent;
                    document.getElementById('searchBtn').click();
                });
            });
        });
    </script>
</body>
</html>
