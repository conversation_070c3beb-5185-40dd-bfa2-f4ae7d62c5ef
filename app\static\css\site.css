/* 基础样式 */
:root {
  --primary-color: #0d6efd;
  --secondary-color: #6c757d;
  --success-color: #198754;
  --info-color: #0dcaf0;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --light-color: #f8f9fa;
  --dark-color: #212529;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.5;
  color: #212529;
  background-color: #f8f9fa;
}

.footer {
  position: relative;
  bottom: 0;
  width: 100%;
  height: 60px;
  line-height: 60px;
  background-color: #f5f5f5;
  margin-top: 2rem;
}

/* 卡片样式 */
.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border: none;
  margin-bottom: 1.5rem;
}

.card-header {
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

/* 项目列表样式 */
.item-card {
  border-left: 4px solid var(--primary-color);
  transition: all 0.3s ease;
}

.item-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.item-card.importance-5 {
  border-left-color: var(--danger-color);
}

.item-card.importance-4 {
  border-left-color: var(--warning-color);
}

.item-card.importance-3 {
  border-left-color: var(--primary-color);
}

.item-card.importance-2 {
  border-left-color: var(--info-color);
}

.item-card.importance-1 {
  border-left-color: var(--secondary-color);
}

/* 标签样式 */
.tag-badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25em 0.6em;
  border-radius: 0.375rem;
  background-color: var(--light-color);
  color: var(--dark-color);
  margin-right: 0.25rem;
  margin-bottom: 0.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tag-badge:hover {
  background-color: var(--primary-color);
  color: white;
}

/* 分类树样式 */
.category-tree {
  list-style: none;
  padding-left: 0;
}

.category-tree ul {
  list-style: none;
  padding-left: 1.5rem;
}

.category-tree li {
  margin-bottom: 0.5rem;
}

.category-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0.375rem 0.75rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.category-item:hover {
  background-color: rgba(13, 110, 253, 0.1);
}

.category-item.active {
  background-color: var(--primary-color);
  color: white;
}

.category-toggle {
  margin-right: 0.5rem;
  cursor: pointer;
}

/* 自定义按钮样式 */
.btn-floating {
  position: fixed;
  right: 2rem;
  bottom: 2rem;
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  z-index: 1030;
}

.btn-floating i {
  font-size: 1.5rem;
}

/* 重要度和紧急度指示器 */
.importance-indicator, .urgency-indicator {
  display: inline-block;
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  margin-right: 0.25rem;
}

.importance-5, .urgency-5 {
  background-color: var(--danger-color);
}

.importance-4, .urgency-4 {
  background-color: var(--warning-color);
}

.importance-3, .urgency-3 {
  background-color: var(--primary-color);
}

.importance-2, .urgency-2 {
  background-color: var(--info-color);
}

.importance-1, .urgency-1 {
  background-color: var(--secondary-color);
}
