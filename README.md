# Flask MongoDB 整合应用

基于Flask和MongoDB的整合应用，支持用户认证、数据管理和插件系统。

## 项目结构

```
flask_mongodb_fixed/
├── app/                    # 主应用包
│   ├── __init__.py        # 应用工厂
│   ├── config/            # 配置模块
│   │   ├── __init__.py
│   │   └── config.py      # 应用配置
│   ├── models/            # 数据模型
│   │   ├── __init__.py
│   │   └── user.py        # 用户模型
│   ├── services/          # 服务层
│   │   ├── __init__.py
│   │   └── db_service.py  # 数据库服务
│   ├── api/               # API蓝图
│   │   ├── __init__.py
│   │   ├── auth.py        # 认证API
│   │   ├── items.py       # 项目管理API
│   │   ├── categories.py  # 分类管理API
│   │   ├── tags.py        # 标签管理API
│   │   └── plugins.py     # 插件管理API
│   ├── static/            # 静态文件
│   └── templates/         # 模板文件
├── app.py                 # 应用入口
├── requirements.txt       # 依赖包
├── architecture.md        # 架构文档
└── README.md             # 项目说明
```

## 功能特性

- **用户认证系统**: 支持用户注册、登录、权限管理
- **数据管理**: 项目、分类、标签的CRUD操作
- **插件系统**: 支持插件的安装、启用、禁用、卸载
- **存储适配**: 支持MongoDB和本地存储两种模式
- **RESTful API**: 完整的API接口设计
- **前端集成**: 静态文件服务和路由

## 环境要求

- Python 3.11+
- MongoDB 4.5.0+ (运行在 localhost:27017)
- Flask 2.3.3+

## 安装和运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 确保MongoDB运行

确保MongoDB服务运行在 `localhost:27017`

### 3. 启动应用

```bash
python app.py
```

应用将在 `http://localhost:5000` 启动

### 4. 访问应用

- 主页: http://localhost:5000/
- 健康检查: http://localhost:5000/health
- API文档: 参考 architecture.md

## 配置

### 环境变量

- `FLASK_ENV`: 运行环境 (development/testing/production)
- `MONGODB_HOST`: MongoDB主机地址 (默认: localhost)
- `MONGODB_PORT`: MongoDB端口 (默认: 27017)
- `MONGODB_DB`: 数据库名称 (默认: integrated_db)
- `STORAGE_MODE`: 存储模式 (mongodb/local, 默认: mongodb)
- `SECRET_KEY`: Flask密钥

### 存储模式

- **MongoDB模式**: 使用MongoDB存储数据，支持多用户
- **本地模式**: 使用本地文件存储，适合单用户使用

## API接口

### 认证API
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/user` - 获取当前用户信息
- `PUT /api/auth/user` - 更新用户信息

### 数据管理API
- `GET /api/items` - 获取项目列表
- `POST /api/items` - 创建项目
- `GET /api/items/<id>` - 获取单个项目
- `PUT /api/items/<id>` - 更新项目
- `DELETE /api/items/<id>` - 删除项目

### 分类管理API
- `GET /api/categories` - 获取分类树
- `POST /api/categories` - 创建分类
- `PUT /api/categories/<id>` - 更新分类
- `DELETE /api/categories/<id>` - 删除分类

### 标签管理API
- `GET /api/tags` - 获取标签列表
- `POST /api/tags` - 创建标签
- `PUT /api/tags/<id>` - 更新标签
- `DELETE /api/tags/<id>` - 删除标签

### 插件管理API
- `GET /api/plugins` - 获取插件列表
- `POST /api/plugins` - 安装插件
- `PUT /api/plugins/<id>` - 更新插件配置
- `DELETE /api/plugins/<id>` - 卸载插件

## 开发

### 开发模式运行

```bash
export FLASK_ENV=development
python app.py
```

### 测试

```bash
export FLASK_ENV=testing
python -m pytest
```

## 部署

参考 architecture.md 中的部署方案。

## 许可证

MIT License
